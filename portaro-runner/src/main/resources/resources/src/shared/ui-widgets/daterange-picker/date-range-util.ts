import type {DateTime} from 'luxon';
import {exists} from 'shared/utils/custom-utils';

type InclusiveStartBracket = '[';
type ExclusiveStartBracket = '(';
type InclusiveEndBracket = ']';
type ExclusiveEndBracket = ')';

type IntervalStartBracket = InclusiveStartBracket | ExclusiveStartBracket;
type IntervalEndBracket = InclusiveEndBracket | ExclusiveEndBracket;

type DateTimeWithZoneString = string & {readonly __brand: 'DateTimeWithZoneString'};
type IntervalStartDate = DateTimeWithZoneString | '-infinity';
type IntervalEndDate = DateTimeWithZoneString | 'infinity';

type EmptyIntervalString = 'empty';

export type IntervalString =
    EmptyIntervalString
    | `${IntervalStartBracket}${IntervalStartDate},${IntervalEndDate}${IntervalEndBracket}`;

interface IntervalObject {
    from?: DateTime | '-infinity'; // Null = -infinity
    to?: DateTime | 'infinity'; // Null = infinity
}

// TODO: This function should take an IntervalString and return an IntervalObject
//  handle excluded by subtracting/adding 1s
//  handle empty
export function parseIntervalStringToObject(interval: IntervalString): IntervalObject {
    if (interval === 'empty') {
        return {};
    }

    // TODO: implement

    return {};
}

// TODO: Both from and date should return as inclusive, when both from and to are null return empty
export function parseObjectToIntervalString(interval: IntervalObject): IntervalString {
    if (!exists(interval) || (!exists(interval.from) && !exists(interval.to))) {
        return 'empty';
    }

    // TODO: Implement

    return 'empty';
}